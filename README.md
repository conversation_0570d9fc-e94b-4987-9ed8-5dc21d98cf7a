<div align="center">

# ✨ Magic Resume ✨

[![License](https://img.shields.io/badge/License-Apache_2.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
![Next.js](https://img.shields.io/badge/Next.js-14.0-black)
![Framer Motion](https://img.shields.io/badge/Framer_Motion-10.0-purple)

简体中文 | [English](./README.en-US.md)

</div>

Magic Resume 是一个现代化的在线简历编辑器，让创建专业简历变得简单有趣。基于 Next.js 和 Motion 构建，支持实时预览和自定义主题。

## 📸 项目截图

![782shots_so](https://github.com/user-attachments/assets/d59f7582-799c-468d-becf-59ee6453acfd)

## ✨ 特性

- 🚀 基于 Next.js 14+ 构建
- 💫 流畅的动画效果 (Motion)
- 🎨 自定义主题支持
- 🌙 深色模式
- 📤 导出为 PDF
- 🔄 实时预览
- 💾 自动保存
- 🔒 硬盘级存储

## 🛠️ 技术栈

- Next.js 14+
- TypeScript
- Motion
- Tiptap
- Tailwind CSS
- Zustand
- Shadcn/ui
- Lucide Icons

## 🚀 快速开始

1. 克隆项目

```bash
<NAME_EMAIL>:JOYCEQL/magic-resume.git
cd magic-resume
```

2. 安装依赖

```bash
pnpm install
```

3. 启动开发服务器

```bash
pnpm dev
```

4. 打开浏览器访问 `http://localhost:3000`

## 📦 构建打包

```bash
pnpm build
```

## ⚡ Vercel 部署

你可以一键部署自己的 Magic Resume 实例：

[![使用 Vercel 部署](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2FJOYCEQL%2Fmagic-resume)

## 🐳 Docker 部署

### Docker Compose

1. 确保你已经安装了 Docker 和 Docker Compose

2. 在项目根目录运行：

```bash
docker compose up -d
```

这将会：

- 自动构建应用镜像
- 在后台启动容器

### Docker Hub

最新版本的 Magic Resume 已经发布在 Docker Hub：

[Docker Hub](https://hub.docker.com/r/siyueqingchen/magic-resume/)

```bash
docker pull siyueqingchen/magic-resume:main
```

## 📝 开源协议

本项目采用 Apache 2.0 协议，但有一些自定义的部分 - 查看 [LICENSE](LICENSE) 了解详情

## 🗺️ 路线图

- [x] AI 辅助编写
- [x] 多语言支持
- [ ] 支持更多简历模板
- [ ] 更多格式导出
- [ ] 自定义模型
- [ ] 智能一页
- [ ] 导入 PDF, Markdown 等
- [ ] 在线简历托管

## 📞 联系方式

可以通过以下方式关注最新动态:

- 作者：SiYue
- X: @GuangzhouY81070
- Discord: 欢迎加入群组 https://discord.gg/9mWgZrW3VN
- 用户群：加微信 qingchensiyue
- 邮箱：<EMAIL>
- 项目主页：https://github.com/JOYCEQL/magic-resume

## 🌟 支持项目

如果这个项目对你有帮助，欢迎点个 star ⭐️
