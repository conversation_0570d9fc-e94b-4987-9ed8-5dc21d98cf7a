<div align="center">

# ✨ Magic Resume ✨

[![License](https://img.shields.io/badge/License-Apache_2.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
![Next.js](https://img.shields.io/badge/Next.js-14.0-black)
![Framer Motion](https://img.shields.io/badge/Framer_Motion-10.0-purple)

[简体中文](./README.md) | English

</div>

Magic Resume is a modern online resume editor that makes creating professional resumes simple and enjoyable. Built with Next.js and Framer Motion, it supports real-time preview and custom themes.

## 📸 Screenshots

![782shots_so](https://github.com/user-attachments/assets/dda52f82-10eb-4f8d-a643-a11c3c4da35f)

## ✨ Features

- 🚀 Built with Next.js 14+
- 💫 Smooth animations (Framer Motion)
- 🎨 Custom theme support
- 📱 Responsive design
- 🌙 Dark mode
- 📤 Export to PDF
- 🔄 Real-time preview
- 💾 Auto-save
- 🔒 Local storage

## 🛠️ Tech Stack

- Next.js 14+
- TypeScript
- Motion
- Tiptap
- Tailwind CSS
- Zustand
- Shadcn/ui
- Lucide Icons

## 🚀 Quick Start

1. Clone the project

```bash
<NAME_EMAIL>:JOYCEQL/magic-resume.git
cd magic-resume
```

2. Install dependencies

```bash
pnpm install
```

3. Start development server

```bash
pnpm dev
```

4. Open browser and visit `http://localhost:3000`

## 📦 Build and Deploy

```bash
pnpm build
```

## ⚡ Deploy with Vercel

You can deploy your own instance of Magic Resume with one click:

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2FJOYCEQL%2Fmagic-resume)

## 🐳 Docker Deployment

### Docker Compose

1. Ensure you have Docker and Docker Compose installed

2. Run the following command in the project root directory:

```bash
docker compose up -d
```

This will:

- Automatically build the application image
- Start the container in the background

### Docker Hub

The latest version of Magic Resume is available on Docker Hub:

[Docker Hub](https://hub.docker.com/r/siyueqingchen/magic-resume/)

```bash
docker pull siyueqingchen/magic-resume:main
```

## 📝 License

This project is licensed under the Apache 2.0 License - see the [LICENSE](LICENSE) file for details

## 🗺️ Roadmap

- [x] AI-assisted writing
- [x] Multi-language support
- [ ] Support for more resume templates
- [ ] Support for more export formats
- [ ] Import PDF, Markdown, etc.
- [ ] Custom model
- [ ] Smart single page
- [ ] Online resume hosting

## 📞 Contact

You can follow the latest updates via:

- Author: Siyue
- X: @GuangzhouY81070
- Discord: Join our community https://discord.gg/9mWgZrW3VN
- Email: <EMAIL>
- Project Homepage: https://github.com/JOYCEQL/magic-resume

## 🌟 Support

If you find this project helpful, please give it a star ⭐️
