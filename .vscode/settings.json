{"css.validate": false, "less.validate": false, "scss.validate": false, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "stylelint.validate": ["css", "less", "postcss", "scss", "sass"], "typescript.tsdk": "./node_modules/typescript/lib", "search.exclude": {"**/node_modules": true, "dist": true, "build": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "i18n-ally.localesPaths": ["src/i18n", "src/i18n/locales"]}