.tiptap {
  min-height: 300px;
  border-radius: 0 0 4px 4px;
  ul,
  ol {
    list-style-type: disc;
    padding: 0 1rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }
  ol {
    list-style-type: decimal;
    padding: 1rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }
  /* Heading styles */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.1;
    margin-top: 2.5rem;
    text-wrap: pretty;
  }

  h1,
  h2 {
    margin-top: 3.5rem;
    margin-bottom: 1.5rem;
  }

  h1 {
    font-size: 1.4rem;
  }

  h2 {
    font-size: 1.2rem;
  }

  h3 {
    font-size: 1.1rem;
  }

  h4,
  h5,
  h6 {
    font-size: 1rem;
  }

  code {
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 0.4rem;
    color: black;
    font-size: 0.85rem;
    padding: 0.25em 0.3em;
  }

  pre {
    background: black;
    border-radius: 0.5rem;
    color: white;
    font-family: "JetBrainsMono", monospace;
    margin: 1.5rem 0;
    padding: 0.75rem 1rem;

    code {
      background: none;
      color: inherit;
      font-size: 0.8rem;
      padding: 0;
    }
  }

  blockquote {
    border-left: 3px solid gray;
    margin: 1.5rem 0;
    padding-left: 1rem;
  }

  hr {
    border: none;
    border-top: 1px solid gray;
    margin: 2rem 0;
  }
}

.control-group {
  border: 1px solid #d1d5db;
  border-bottom: none;
  background-color: #403d39;
  border-radius: 4px 4px 0 0;
  overflow: hidden;
  .button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    padding: 4px;

    .lucide {
      padding: 4px 6px;
      cursor: pointer;
      color: #fff;
      width: 28px;
      height: 28px;
      &.is-active {
        font-weight: 700;
        color: #2ec4b6;
      }
      &:hover {
        border-radius: 4px;
        background-color: #e26d5c;
      }
    }
  }
}
[contenteditable]:focus {
  outline: none;
}
