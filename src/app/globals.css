@tailwind base;
@tailwind components;
@tailwind utilities;

/* 隐藏数字输入框自带的上下箭头 */
@layer utilities {
  .no-spinner::-webkit-inner-spin-button,
  .no-spinner::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .no-spinner {
    -moz-appearance: textfield;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    /* --primary: 262.1 83.3% 57.8%; */
    --primary: 240 5.9% 10%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    /* --ring: 262.1 83.3% 57.8%; */
    --ring: 240 5.9% 10%;
    --radius: 0.75rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  /* 通用滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  /* hide vertical scrollbar */
  /* ::-webkit-scrollbar-thumb:vertical {
    background: transparent;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  *:hover::-webkit-scrollbar-thumb:vertical {
    background: rgba(193, 193, 193, 0.5);
  }

  ::-webkit-scrollbar-thumb:vertical:hover {
    background: rgba(193, 193, 193, 0.8);
  } */

  /* show horizontal scrollbar */
  ::-webkit-scrollbar-thumb:horizontal {
    background: rgba(193, 193, 193, 0.5);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:horizontal:hover {
    background: rgba(193, 193, 193, 0.8);
  }

  .dark {
    /* & ::-webkit-scrollbar-thumb:vertical {
      background: transparent;
    } */
    /* 
    & *:hover::-webkit-scrollbar-thumb:vertical {
      background: rgba(102, 102, 102, 0.5);
    }

    & ::-webkit-scrollbar-thumb:vertical:hover {
      background: rgba(102, 102, 102, 0.8);
    } */

    & ::-webkit-scrollbar-thumb:horizontal {
      background: rgba(102, 102, 102, 0.5);
    }

    & ::-webkit-scrollbar-thumb:horizontal:hover {
      background: rgba(102, 102, 102, 0.8);
    }

    --background: 240 5.9% 10%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 70% 50.4%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  ::selection {
    @apply text-primary-foreground bg-primary;
  }

  ::-moz-selection {
    @apply text-primary-foreground bg-primary;
  }

  /* Fix Chrome extension screenshot problem, no impact on this project */
  img,
  svg {
    @apply inline-block;
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes tilt {
  0%,
  50%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(0.5deg);
  }
  75% {
    transform: rotate(-0.5deg);
  }
}

@keyframes gradient-xy {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animate-tilt {
  animation: tilt 10s infinite linear;
}

.animate-gradient-xy {
  animation: gradient-xy 15s ease infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* .custom-list,
.custom-list-ordered {
  list-style-type: none;
}

.custom-list li::marker,
.custom-list-ordered li::marker {
  display: none;
} */

/* .custom-list li::before {
  content: "•";
  display: inline-block;
  margin-right: 10px;
} */

/* .custom-list-ordered li::before {
  content: counter(list-0, decimal) ". ";
  counter-increment: list-0;
  display: inline-block;
  margin-right: 5px;
} */

.custom-list {
  list-style-type: disc;
  padding: 0 1.2rem;
}

.custom-list-ordered {
  list-style-position: inside;
  list-style-type: decimal;

  p {
    display: inline;
  }
  /* 次级需要特殊处理 */
  .custom-list-ordered {
    padding: 0 1.2rem;
  }
}
