{"name": "magic-resume", "version": "1.0.0", "private": true, "packageManager": "pnpm@10.3.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.1.3", "@sparticuz/chromium": "^131.0.0", "@tiptap/extension-bullet-list": "^2.10.2", "@tiptap/extension-color": "^2.4.0", "@tiptap/extension-highlight": "^2.9.1", "@tiptap/extension-list-item": "^2.4.0", "@tiptap/extension-ordered-list": "^2.10.2", "@tiptap/extension-text-align": "^2.4.0", "@tiptap/extension-text-style": "^2.4.0", "@tiptap/extension-underline": "^2.9.1", "@tiptap/pm": "^2.4.0", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@vercel/analytics": "^1.5.0", "chrome-aws-lambda": "^10.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^3.6.0", "dayjs": "^1.11.12", "framer-motion": "^11.11.10", "html2pdf.js": "^0.10.2", "lodash": "^4.17.21", "lucide-react": "^0.379.0", "mark.js": "^8.11.1", "next": "14.2.3", "next-intl": "^3.26.3", "next-themes": "^0.4.3", "puppeteer": "^23.9.0", "puppeteer-core": "^23.9.0", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-resizable-panels": "^2.0.20", "sharp": "^0.33.5", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.5", "vaul": "^1.1.1", "zustand": "^4.5.4"}, "devDependencies": {"@types/lodash": "^4.17.13", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "postcss-normalize": "^13.0.1", "sass": "^1.77.4", "tailwindcss": "^3.4.1", "typescript": "^5"}}